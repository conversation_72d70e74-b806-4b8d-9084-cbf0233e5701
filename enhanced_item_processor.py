"""
Enhanced Item Processor with Entity-Type-Specific Flows
World-class data quality through intelligent routing and type-specific enhancement
"""

import logging
import re
from typing import Dict, Any, Optional, List
from urllib.parse import urlparse
import requests
from bs4 import BeautifulSoup
from entity_type_detector import EntityTypeDetector
from type_specific_enhancers import TypeSpecificEnhancerFactory
from advanced_data_extractor import AdvancedDataExtractor
from centralized_url_resolver import CentralizedURLResolver
from contact_info_extractor import ContactInfoExtractor

class EnhancedItemProcessor:
    def __init__(self, client, enrichment_service, taxonomy_service=None, api_key=None):
        self.client = client
        self.enrichment_service = enrichment_service

        # CRITICAL FIX: Use TaxonomyManager with fallback logic instead of EnhancedTaxonomyService
        # The TaxonomyManager ensures we always have at least 1 category and 1 tag
        if not hasattr(client, 'process_taxonomy_suggestions'):
            self.logger.error("❌ AI Navigator client missing process_taxonomy_suggestions method!")
            raise ValueError("Client must have process_taxonomy_suggestions method")
        
        # The client already has taxonomy manager integrated, so we don't need a separate service
        # Just verify it's working
        self.taxonomy_service = None  # We'll use client.process_taxonomy_suggestions directly

        self.logger = logging.getLogger(__name__)
        
        # Initialize new components
        self.entity_detector = EntityTypeDetector()
        self.data_extractor = AdvancedDataExtractor()
        self.url_resolver = CentralizedURLResolver()
        self.contact_extractor = ContactInfoExtractor()

        self.logger.info("✅ Enhanced Item Processor initialized with TaxonomyManager fallback logic")
    
    def _resolve_redirect_url(self, url: str) -> str:
        """Resolve redirect URLs to get the final destination using centralized resolver"""
        try:
            # Use centralized URL resolver for robust redirect handling
            if self.url_resolver.is_redirect_url(url):
                if 'futuretools.link' in url or 'futuretools.io' in url:
                    return self.url_resolver.resolve_futuretools_redirect(url)
                else:
                    return self.url_resolver.resolve_final_url(url)
            else:
                return self.url_resolver.resolve_final_url(url)
        except Exception as e:
            self.logger.warning(f"Could not resolve URL {url}: {e}")
            return url

    def _extract_website_data(self, url: str) -> Dict[str, Any]:
        """Extract comprehensive data from the actual website including contact info"""
        website_data = {}

        try:
            # Fetch website content once for all extractions
            import requests
            response = requests.get(url, timeout=15, headers={
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            })
            content = response.text

            # Extract logo
            try:
                from logo_extraction_service import LogoExtractionService
                logo_service = LogoExtractionService()
                logo_url = logo_service.extract_logo_url(url, content)
                if logo_url:
                    website_data['logo_url'] = logo_url
                else:
                    website_data['logo_url'] = f"https://www.google.com/s2/favicons?domain={url}&sz=128"
            except ImportError:
                website_data['logo_url'] = f"https://www.google.com/s2/favicons?domain={url}&sz=128"

            # Extract contact information
            contact_info = self.contact_extractor.extract_contact_info(url, content)
            website_data.update(contact_info)

            # Extract additional URLs
            try:
                from url_discovery_service import URLDiscoveryService
                url_service = URLDiscoveryService()
                discovered_urls = url_service.discover_urls(url, content)
                website_data.update(discovered_urls)
            except ImportError:
                self.logger.warning("URL discovery service not available")

        except Exception as e:
            self.logger.warning(f"Error extracting website data from {url}: {e}")
            website_data['logo_url'] = f"https://www.google.com/s2/favicons?domain={url}&sz=128"
            website_data['support_email'] = None
            website_data['has_live_chat'] = False
            website_data['community_url'] = None
            website_data['social_links'] = {}

        return website_data

    def _create_unique_tool_name(self, name: str) -> str:
        """Create a unique, clean tool name"""
        if not name:
            return "Unknown Tool"
            
        # Clean the name
        clean_name = re.sub(r'[^\w\s\-\.]', '', name)
        clean_name = re.sub(r'\s+', ' ', clean_name).strip()
        
        return clean_name

    def _get_entity_type_id(self) -> str:
        """Get the entity type ID for AI tools dynamically from API"""
        entity_type_id = self.client.get_ai_tool_entity_type_id()
        if entity_type_id:
            return entity_type_id
        else:
            self.logger.error("Could not get AI tool entity type ID from API")
            # Fallback to known ID if API call fails
            return "fd181400-c9e6-431c-a8bd-c068d0491aba"

    def process_lead_item(self, lead_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        Process lead item with intelligent entity type detection and specialized enhancement

        Step 1: Check if Entity Already Exists (to save API credits)
        Step 2: Detect Entity Type
        Step 3: Enhance with Type-Specific Flow
        Step 4: Create Type-Appropriate Schema
        Step 5: Submit to Database
        """

        tool_name = lead_data.get('tool_name_on_directory', 'Unknown Tool')
        external_url = lead_data.get('external_website_url', '')

        self.logger.info(f"🔄 Processing: {tool_name}")
        self.logger.info(f"   URL: {external_url}")

        try:
            # === STEP 1: CHECK IF ENTITY ALREADY EXISTS ===
            self.logger.info(f"🔍 Step 1: Checking if entity already exists for {tool_name}")

            existing_entity = self._find_existing_entity(tool_name, external_url)
            if existing_entity:
                self.logger.info(f"   ✅ Entity already exists with ID: {existing_entity.get('id')}")
                self.logger.info(f"   💰 Skipping AI enhancement to save API credits")
                return None  # Skip processing to avoid duplicate and save credits

            # Generate unique name if needed
            unique_tool_name = self._ensure_unique_name(tool_name)
            if unique_tool_name != tool_name:
                self.logger.info(f"   🔄 Using unique name: '{unique_tool_name}' (original: '{tool_name}')")
                tool_name = unique_tool_name

            self.logger.info(f"   ✅ Entity does not exist, proceeding with enhancement")
            # === STEP 2: INTELLIGENT ENTITY TYPE DETECTION ===
            self.logger.info(f"🎯 Step 2: Detecting entity type for {tool_name}")
            
            entity_type = self.entity_detector.detect_entity_type(
                tool_name=tool_name,
                website_url=external_url,
                description=lead_data.get('description', '')
            )
            
            self.logger.info(f"   Detected type: {entity_type}")
            
            # === STEP 3: GET ENTITY TYPE ID ===
            entity_type_id = self.entity_detector.get_entity_type_id(entity_type, self.client)
            if not entity_type_id:
                self.logger.error(f"   ❌ Could not get entity type ID for {entity_type}")
                return None
            
            self.logger.info(f"   Entity type ID: {entity_type_id}")
            
            # === STEP 4: RESOLVE URL AND EXTRACT WEBSITE DATA ===
            self.logger.info(f"🌐 Step 3: Resolving URL and extracting website data")
            
            final_url = self._resolve_redirect_url(external_url)
            website_data = self._extract_website_data(final_url)
            
            self.logger.info(f"   Resolved URL: {final_url}")
            self.logger.info(f"   Website data extracted: {len(website_data)} fields")
            
            # === STEP 5: TYPE-SPECIFIC ENHANCEMENT ===
            self.logger.info(f"🚀 Step 4: Type-specific enhancement for {entity_type}")

            # Get type-specific enhancer
            enhancer = TypeSpecificEnhancerFactory.create_enhancer(
                entity_type=entity_type,
                api_key=self.enrichment_service.api_key
            )

            # Enhance with type-specific prompts and schemas
            enriched_data = enhancer.enhance_entity(
                name=tool_name,
                website_url=final_url,
                initial_data=str(lead_data)
            )

            self.logger.info(f"   ✅ Enhanced with {len(enriched_data)} comprehensive fields")

            # === STEP 6: ADVANCED DATA EXTRACTION ===
            self.logger.info(f"📊 Step 5: Advanced data extraction and cleaning")
            
            # Extract clean social links (no more HTML fragments!)
            clean_social_links = self.data_extractor.extract_clean_social_links(
                website_content=str(website_data),
                website_url=final_url
            )
            
            # Create comprehensive description (400-600 chars)
            comprehensive_description = self.data_extractor.create_comprehensive_description(
                enhanced_data=enriched_data,
                website_data=website_data,
                entity_type=entity_type
            )
            
            # Extract additional URLs
            additional_urls = self.data_extractor.extract_additional_urls(
                website_content=str(website_data),
                base_url=final_url
            )
            
            self.logger.info(f"   Social links: {len(clean_social_links)} platforms")
            self.logger.info(f"   Description: {len(comprehensive_description)} chars")
            self.logger.info(f"   Additional URLs: {len([u for u in additional_urls.values() if u])} found")
            
            # === STEP 7: ENHANCED TAXONOMY MANAGEMENT ===
            category_ids = []
            tag_ids = []
            feature_ids = []
            # Note: features field removed - API doesn't support it

            if entity_type in ['ai-tool', 'software', 'platform']:
                self.logger.info(f"🏷️  Step 6: Enhanced taxonomy management for {entity_type}")

                # Use the new taxonomy management flow with create-or-find logic
                suggested_categories = enriched_data.get('categories', [])

                # Enhanced tag classification - improve AI-powered tag analysis
                initial_tags = enriched_data.get('tags', [])
                suggested_tags = self._improve_tag_classification(
                    tool_name=tool_name,
                    initial_tags=initial_tags,
                    categories=suggested_categories,
                    description=enriched_data.get('description', ''),
                    use_cases=enriched_data.get('use_cases', []),
                    key_features=enriched_data.get('key_features', [])
                )

                # Enhanced feature extraction - combine key_features with AI-powered analysis
                suggested_features = enriched_data.get('key_features', [])

                # Add AI-powered feature extraction from use_cases and description
                use_cases = enriched_data.get('use_cases', [])
                description = enriched_data.get('description', '')

                # Extract additional features from use cases and description using AI
                if use_cases or description:
                    ai_extracted_features = self._extract_features_with_ai(
                        tool_name=tool_name,
                        use_cases=use_cases,
                        description=description,
                        existing_features=suggested_features
                    )
                    suggested_features.extend(ai_extracted_features)

                # Remove duplicates while preserving order
                suggested_features = list(dict.fromkeys(suggested_features))

                self.logger.info(f"   🎯 Processing {len(suggested_features)} features: {suggested_features[:3]}...")

                # Process taxonomy suggestions through the client
                taxonomy_result = self.client.process_taxonomy_suggestions(
                    suggested_categories=suggested_categories,
                    suggested_features=suggested_features,
                    suggested_tags=suggested_tags
                )

                # Extract UUIDs and objects from taxonomy result
                category_ids = [item.uuid for item in taxonomy_result.categories]
                feature_ids = [item.uuid for item in taxonomy_result.features]
                tag_ids = [item.uuid for item in taxonomy_result.tags]

                # Note: features objects removed - API doesn't support features field

                # Log results and any errors
                if taxonomy_result.errors:
                    self.logger.warning(f"   Taxonomy errors: {taxonomy_result.errors}")

                self.logger.info(f"   ✅ Taxonomy processed: {len(category_ids)} categories, {len(tag_ids)} tags, {len(feature_ids)} features")
                self.logger.info(f"   📊 Created/Found: {len(taxonomy_result.categories)} categories, {len(taxonomy_result.features)} features, {len(taxonomy_result.tags)} tags")

            # === STEP 8: BUILD TYPE-SPECIFIC ENTITY DTO ===
            self.logger.info(f"🏗️  Step 7: Building {entity_type}-specific entity DTO")
            
            entity_dto = self._build_entity_dto(
                entity_type=entity_type,
                entity_type_id=entity_type_id,
                tool_name=tool_name,
                final_url=final_url,
                enriched_data=enriched_data,
                website_data=website_data,
                clean_social_links=clean_social_links,
                comprehensive_description=comprehensive_description,
                additional_urls=additional_urls,
                category_ids=category_ids,
                tag_ids=tag_ids,
                feature_ids=feature_ids,

                lead_data=lead_data
            )
            
            # Clean null values
            entity_dto = self._clean_null_values(entity_dto)
            
            self.logger.info(f"   ✅ Entity DTO created with {len(entity_dto)} fields")
            self.logger.info(f"🎉 Processing complete for {tool_name} as {entity_type}")
            
            return entity_dto
            
        except Exception as e:
            import traceback
            self.logger.error(f"❌ Error processing {tool_name}: {type(e).__name__}: {str(e)}")
            self.logger.error(f"   Full traceback: {traceback.format_exc()}")

            # Re-raise with more context for better debugging
            raise Exception(f"Processing failed for {tool_name}: {type(e).__name__}: {str(e)}") from e
    
    def _scrape_website_data(self, website_url: str) -> Dict[str, Any]:
        """Scrape basic information from the tool's website with enhanced logo detection"""
        try:
            headers = {
                'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
            }
            
            response = requests.get(website_url, headers=headers, timeout=15)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # Extract basic website data
            data = {}
            
            # Enhanced logo detection with multiple methods
            logo_url = self._extract_logo_url(soup, website_url)
            if logo_url:
                data['logo_url'] = logo_url
            
            # Try to find description from meta tags
            meta_desc = soup.find('meta', attrs={'name': 'description'})
            if meta_desc:
                data['description'] = meta_desc.get('content', '')
            
            # Look for common URLs
            links = soup.find_all('a')
            for link in links:
                href = link.get('href', '').lower()
                text = link.get_text().lower()
                
                if 'pricing' in href or 'pricing' in text:
                    data['pricing_url'] = requests.compat.urljoin(website_url, link['href'])
                elif 'contact' in href or 'contact' in text:
                    data['contact_url'] = requests.compat.urljoin(website_url, link['href'])
                elif 'privacy' in href or 'privacy' in text:
                    data['privacy_policy_url'] = requests.compat.urljoin(website_url, link['href'])
                elif 'docs' in href or 'documentation' in href:
                    data['documentation_url'] = requests.compat.urljoin(website_url, link['href'])
                elif 'community' in href or 'discord' in href or 'slack' in href:
                    data['community_url'] = requests.compat.urljoin(website_url, link['href'])
            
            # Look for support email
            email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
            page_text = soup.get_text()
            emails = re.findall(email_pattern, page_text)
            
            for email in emails:
                if any(word in email.lower() for word in ['support', 'help', 'contact', 'info']):
                    data['support_email'] = email
                    break
            
            return data
            
        except Exception as e:
            self.logger.warning(f"Could not scrape website data from {website_url}: {str(e)}")
            return {}
    
    def _is_valid_url(self, url: str) -> bool:
        """Validate that a URL is properly formatted"""
        if not url or not isinstance(url, str):
            return False
        return url.startswith(('http://', 'https://')) and len(url) > 10
    
    def _get_guaranteed_fallback_logo(self, tool_name: str) -> str:
        """Generate a guaranteed working fallback logo"""
        if tool_name:
            # Extract initials from tool name
            import re
            words = re.findall(r'\b\w', tool_name.upper())
            initials = ''.join(words[:2])  # Take first 2 initials
            return f"https://ui-avatars.com/api/?name={initials}&size=128&background=6366f1&color=ffffff&bold=true&format=png"
        else:
            return "https://ui-avatars.com/api/?name=AI&size=128&background=6366f1&color=ffffff&bold=true&format=png"
    
    def _build_entity_dto(self, entity_type: str, entity_type_id: str, tool_name: str,
                         final_url: str, enriched_data: Dict[str, Any], website_data: Dict[str, Any],
                         clean_social_links: Dict[str, str], comprehensive_description: str,
                         additional_urls: Dict[str, str], category_ids: list, tag_ids: list,
                         feature_ids: list, lead_data: Dict[str, Any]) -> Dict[str, Any]:
        """Build entity DTO based on entity type"""
        
        # Base fields for all entity types
        base_dto = {
            "name": enriched_data.get('name', tool_name),
            "website_url": final_url,
            "entity_type_id": entity_type_id,
            "short_description": self._create_compelling_short_description(enriched_data, tool_name),
            "description": comprehensive_description,
            "logo_url": website_data.get('logo_url'),
            "status": "ACTIVE"
        }
        
        # Add type-specific fields
        if entity_type == 'ai-tool':
            return self._build_ai_tool_dto(base_dto, enriched_data, website_data, clean_social_links,
                                         additional_urls, category_ids, tag_ids, feature_ids, lead_data)
        
        elif entity_type == 'research-paper':
            return self._build_research_paper_dto(base_dto, enriched_data, additional_urls)
        
        elif entity_type == 'hardware':
            return self._build_hardware_dto(base_dto, enriched_data, additional_urls)
        
        elif entity_type == 'job':
            return self._build_job_dto(base_dto, enriched_data, additional_urls)
        
        elif entity_type == 'event':
            return self._build_event_dto(base_dto, enriched_data, additional_urls)
        
        else:
            # Default to AI tool structure
            return self._build_ai_tool_dto(base_dto, enriched_data, website_data, clean_social_links,
                                         additional_urls, category_ids, tag_ids, feature_ids, lead_data)
    
    def _build_ai_tool_dto(self, base_dto: Dict[str, Any], enriched_data: Dict[str, Any],
                          website_data: Dict[str, Any], clean_social_links: Dict[str, str],
                          additional_urls: Dict[str, str], category_ids: list, tag_ids: list,
                          feature_ids: list, lead_data: Dict[str, Any]) -> Dict[str, Any]:
        """Build comprehensive AI Tool DTO"""
        
        ai_tool_dto = base_dto.copy()
        ai_tool_dto.update({
            # URLs
            "documentation_url": self._validate_url(additional_urls.get('documentation_url')),
            "contact_url": self._validate_url(additional_urls.get('contact_url')),
            "privacy_policy_url": self._validate_url(additional_urls.get('privacy_policy_url')),
            
            # Company Information
            "founded_year": self._normalize_founded_year(enriched_data.get('founded_year')),
            "social_links": clean_social_links,
            
            # Taxonomy
            "category_ids": category_ids,
            "tag_ids": tag_ids,
            "feature_ids": feature_ids,
            
            # SEO
            "meta_title": f"{enriched_data.get('name', base_dto['name'])} | AI Navigator - {enriched_data.get('categories', ['AI Tool'])[0] if enriched_data.get('categories') else 'AI Tool'}",
            "meta_description": self._create_seo_meta_description(enriched_data, base_dto['name']),
            
            # Business Data
            "employee_count_range": self._normalize_employee_count(enriched_data.get('employee_count_range')),
            "funding_stage": self._normalize_funding_stage(enriched_data.get('funding_stage')),
            "location_summary": enriched_data.get('location_summary'),
            
            # Tracking - Use actual website URL, not redirect links
            "ref_link": base_dto['website_url'],  # Use resolved final URL instead of redirect
            "affiliate_status": "NONE",
            
            # Reviews
            "scraped_review_sentiment_label": enriched_data.get('review_sentiment_label'),
            "scraped_review_sentiment_score": self._normalize_sentiment_score(enriched_data.get('review_sentiment_score')),
            "scraped_review_count": self._extract_accurate_review_count(enriched_data),
            
            # Tool Details
            "tool_details": self._build_comprehensive_tool_details(enriched_data, website_data)
        })
        
        return ai_tool_dto

    def _build_research_paper_dto(self, base_dto: Dict[str, Any], enriched_data: Dict[str, Any],
                                 additional_urls: Dict[str, str]) -> Dict[str, Any]:
        """Build Research Paper DTO"""
        
        research_dto = base_dto.copy()
        research_dto.update({
            "research_paper_details": {
                "publication_date": enriched_data.get('publication_date'),
                "doi": enriched_data.get('doi'),
                "authors": enriched_data.get('authors', []),
                "research_areas": enriched_data.get('research_areas', []),
                "publication_venues": enriched_data.get('publication_venues', []),
                "keywords": enriched_data.get('keywords', []),
                "arxiv_id": enriched_data.get('arxiv_id'),
                "abstract": enriched_data.get('abstract', ''),
                "journal_or_conference": enriched_data.get('journal_or_conference'),
                "pdf_url": enriched_data.get('pdf_url'),
                "citation_count": enriched_data.get('citation_count')
            }
        })
        
        return research_dto

    def _build_hardware_dto(self, base_dto: Dict[str, Any], enriched_data: Dict[str, Any],
                           additional_urls: Dict[str, str]) -> Dict[str, Any]:
        """Build Hardware DTO"""
        
        hardware_dto = base_dto.copy()
        hardware_dto.update({
            "hardware_details": {
                "hardware_type": enriched_data.get('hardware_type'),
                "manufacturer": enriched_data.get('manufacturer'),
                "release_date": enriched_data.get('release_date'),
                "specifications": enriched_data.get('specifications', {}),
                "datasheet_url": enriched_data.get('datasheet_url'),
                "price_range": enriched_data.get('price_range'),
                "memory": enriched_data.get('specifications', {}).get('memory'),
                "processor": enriched_data.get('specifications', {}).get('cores'),
                "power_consumption": enriched_data.get('specifications', {}).get('power_consumption'),
                "availability": enriched_data.get('availability'),
                "price": enriched_data.get('msrp'),
                "use_cases": enriched_data.get('use_cases', [])
            }
        })
        
        return hardware_dto

    def _build_job_dto(self, base_dto: Dict[str, Any], enriched_data: Dict[str, Any],
                      additional_urls: Dict[str, str]) -> Dict[str, Any]:
        """Build Job DTO"""
        
        job_dto = base_dto.copy()
        job_dto.update({
            "job_details": {
                "job_title": enriched_data.get('job_title'),
                "company_name": enriched_data.get('company_name'),
                "employment_types": enriched_data.get('employment_types', []),
                "experience_level": enriched_data.get('experience_level'),
                "location_types": enriched_data.get('location_types', []),
                "salary_min": enriched_data.get('salary_min'),
                "salary_max": enriched_data.get('salary_max'),
                "application_url": enriched_data.get('application_url'),
                "job_description": base_dto['description'],
                "is_remote": enriched_data.get('is_remote', False),
                "location": enriched_data.get('location'),
                "job_type": enriched_data.get('job_type'),
                "key_responsibilities": enriched_data.get('key_responsibilities', []),
                "required_skills": enriched_data.get('required_skills', []),
                "benefits": enriched_data.get('benefits', []),
                "remote_policy": enriched_data.get('remote_policy'),
                "visa_sponsorship": enriched_data.get('visa_sponsorship', False)
            }
        })
        
        return job_dto

    def _build_event_dto(self, base_dto: Dict[str, Any], enriched_data: Dict[str, Any],
                        additional_urls: Dict[str, str]) -> Dict[str, Any]:
        """Build Event DTO"""
        
        event_dto = base_dto.copy()
        event_dto.update({
            "event_details": {
                "event_type": enriched_data.get('event_type'),
                "start_date": enriched_data.get('start_date'),
                "end_date": enriched_data.get('end_date'),
                "location": enriched_data.get('location'),
                "is_online": enriched_data.get('is_online', False),
                "event_format": enriched_data.get('event_format'),
                "registration_required": enriched_data.get('registration_required', True),
                "registration_url": enriched_data.get('registration_url'),
                "capacity": enriched_data.get('capacity'),
                "organizer": enriched_data.get('organizer'),
                "key_speakers": enriched_data.get('key_speakers', []),
                "target_audience": enriched_data.get('target_audience', []),
                "topics": enriched_data.get('topics', []),
                "price": enriched_data.get('ticket_price')
            }
        })
        
        return event_dto

    def _extract_logo_url(self, soup: BeautifulSoup, website_url: str) -> Optional[str]:
        """Enhanced logo extraction with multiple fallback methods"""
        from urllib.parse import urljoin, urlparse
        
        # Method 1: Look for specific logo selectors
        logo_selectors = [
            'img[alt*="logo" i]',
            'img[class*="logo" i]',
            'img[src*="logo" i]',
            '.logo img',
            'header img',
            '.header img',
            '.navbar img',
            '.nav img',
            'img[alt*="brand" i]',
            'img[class*="brand" i]',
            '.brand img',
            '[class*="logo"] img',
            'a[class*="logo"] img',
            'div[class*="logo"] img'
        ]
        
        for selector in logo_selectors:
            logo_img = soup.select_one(selector)
            if logo_img and logo_img.get('src'):
                logo_url = logo_img['src']
                # Convert relative URLs to absolute
                if not logo_url.startswith('http'):
                    logo_url = urljoin(website_url, logo_url)
                
                # Validate the logo URL
                if self._validate_logo_url(logo_url):
                    self.logger.info(f"Found logo via selector '{selector}': {logo_url}")
                    return logo_url
        
        # Method 2: Look for Apple touch icons and favicons
        icon_selectors = [
            'link[rel="apple-touch-icon"]',
            'link[rel="apple-touch-icon-precomposed"]', 
            'link[rel="icon"]',
            'link[rel="shortcut icon"]'
        ]
        
        for selector in icon_selectors:
            icon_link = soup.select_one(selector)
            if icon_link and icon_link.get('href'):
                icon_url = icon_link['href']
                if not icon_url.startswith('http'):
                    icon_url = urljoin(website_url, icon_url)
                
                # Only use if it's a reasonable size (not tiny favicon)
                sizes = icon_link.get('sizes', '')
                if any(size in sizes for size in ['180x180', '152x152', '144x144', '120x120']):
                    if self._validate_logo_url(icon_url):
                        self.logger.info(f"Found logo via icon '{selector}': {icon_url}")
                        return icon_url
        
        # Method 3: Try common logo file paths
        domain = urlparse(website_url).netloc
        common_logo_paths = [
            '/logo.png',
            '/logo.svg', 
            '/assets/logo.png',
            '/assets/logo.svg',
            '/static/logo.png',
            '/static/logo.svg',
            '/images/logo.png',
            '/images/logo.svg',
            '/img/logo.png',
            '/img/logo.svg'
        ]
        
        for path in common_logo_paths:
            logo_url = urljoin(website_url, path)
            if self._validate_logo_url(logo_url):
                self.logger.info(f"Found logo via common path: {logo_url}")
                return logo_url
        
        # Method 4: Use external logo services as fallback
        fallback_logo = self._get_fallback_logo(website_url)
        if fallback_logo:
            return fallback_logo
        
        self.logger.warning(f"Could not find logo for {website_url}")
        return None
    
    def _validate_logo_url(self, logo_url: str) -> bool:
        """Validate that a logo URL is accessible and is an image"""
        try:
            response = requests.head(logo_url, timeout=10)
            
            # Check if URL is accessible
            if response.status_code != 200:
                return False
            
            # Check if it's an image
            content_type = response.headers.get('content-type', '').lower()
            if any(img_type in content_type for img_type in ['image/', 'png', 'jpg', 'jpeg', 'svg', 'gif', 'webp']):
                return True
            
            return False
            
        except Exception:
            return False
    
    def _get_fallback_logo(self, website_url: str) -> Optional[str]:
        """Get fallback logo using external services"""
        from urllib.parse import urlparse
        
        domain = urlparse(website_url).netloc
        
        # Method 1: Clearbit Logo API (free, no auth required)
        clearbit_url = f"https://logo.clearbit.com/{domain}"
        if self._validate_logo_url(clearbit_url):
            self.logger.info(f"Found logo via Clearbit: {clearbit_url}")
            return clearbit_url
        
        # Method 2: Google Favicon service (high quality favicons)
        google_favicon_url = f"https://www.google.com/s2/favicons?domain={domain}&sz=128"
        # Note: Google favicon always returns something, so we'll use it as last resort
        self.logger.info(f"Using Google favicon as fallback: {google_favicon_url}")
        return google_favicon_url
    
    def _find_existing_entity(self, name: str, website_url: str) -> Optional[Dict[str, Any]]:
        """
        Check if entity already exists with improved duplicate detection logic.
        Primary focus on URL matching, with more precise name matching as secondary.
        """
        try:
            self.logger.info(f"   🔍 Searching for existing entity: {name}")
            self.logger.info(f"   🌐 URL to check: {website_url}")

            # Use cached entities if available
            if not hasattr(self, '_entity_cache') or not self._entity_cache:
                self._build_entity_cache()

            # Primary check: Exact URL match (most reliable)
            if website_url:
                normalized_url = self._normalize_url(website_url)
                for entity in self._entity_cache:
                    entity_url = entity.get('website_url', '').strip()
                    if entity_url and self._normalize_url(entity_url) == normalized_url:
                        self.logger.info(f"   ✅ Found existing entity by URL match: {entity.get('id')} ('{entity.get('name')}')")
                        self.logger.info(f"   📊 Match reason: Exact URL match")
                        return entity

            # Secondary check: Strict name matching with URL validation
            if name:
                normalized_name = self._normalize_name_for_matching(name)
                for entity in self._entity_cache:
                    entity_name = entity.get('name', '').strip()
                    if entity_name and self._normalize_name_for_matching(entity_name) == normalized_name:
                        # Additional validation: check if URLs are similar (to avoid false positives)
                        entity_url = entity.get('website_url', '').strip()

                        # If existing entity has no URL, enhance it with new data instead of skipping
                        if not entity_url:
                            self.logger.info(f"   🔄 Found existing entity with same name but no URL - will enhance it:")
                            self.logger.info(f"      Existing: '{entity_name}' -> (empty)")
                            self.logger.info(f"      New: '{name}' -> {website_url}")
                            self.logger.info(f"   ✨ Proceeding with enhancement to add missing URL and data")
                            # Store the existing entity ID for potential updates
                            existing_entity_id = entity.get('id')
                            # Don't return here - continue with processing to enhance the existing entity

                        if self._urls_are_similar(website_url, entity_url):
                            self.logger.info(f"   ✅ Found existing entity by name+URL similarity: {entity.get('id')} ('{entity_name}')")
                            self.logger.info(f"   📊 Match reason: Exact name match with URL similarity")
                            return entity
                        else:
                            self.logger.info(f"   ⚠️  Name match found but URLs differ significantly:")
                            self.logger.info(f"      Existing: '{entity_name}' -> {entity_url}")
                            self.logger.info(f"      New: '{name}' -> {website_url}")
                            self.logger.info(f"   ➡️  Treating as different entities")

            self.logger.info(f"   ✅ No existing entity found (checked {len(self._entity_cache)} cached entities)")
            return None

        except Exception as e:
            self.logger.error(f"   ❌ Error checking existing entity: {str(e)}")
            # Fallback to non-cached search if cache fails
            return self._fallback_entity_search(name, website_url)

    def _build_entity_cache(self):
        """Build a cache of existing entities for efficient duplicate detection"""
        try:
            self.logger.info("   🔄 Building entity cache for duplicate detection...")
            
            headers = self.client._get_headers()
            self._entity_cache = []
            
            page = 1
            page_size = 100  # Larger page size for efficiency
            max_pages = 10   # Check up to 1000 entities
            
            while page <= max_pages:
                try:
                    response = requests.get(
                        f"{self.client.base_url}/entities",
                        headers=headers,
                        params={"page": page, "limit": page_size},
                        timeout=20
                    )
                    
                    if response.status_code == 200:
                        result = response.json()
                        entities = result.get('data', []) if isinstance(result, dict) else result
                        
                        if not entities:
                            break
                            
                        self._entity_cache.extend(entities)
                        page += 1
                        
                    else:
                        self.logger.warning(f"   ⚠️  Failed to fetch entities page {page}: {response.status_code}")
                        break
                        
                except requests.exceptions.RequestException as e:
                    self.logger.warning(f"   ⚠️  Request failed for page {page}: {str(e)}")
                    break
            
            self.logger.info(f"   ✅ Entity cache built: {len(self._entity_cache)} entities cached")
            
        except Exception as e:
            self.logger.error(f"   ❌ Error building entity cache: {str(e)}")
            self._entity_cache = []

    def _normalize_url(self, url: str) -> str:
        """Normalize URL for comparison"""
        if not url:
            return ""
        
        # Remove protocol, www, trailing slashes, and convert to lowercase
        normalized = url.lower()
        normalized = normalized.replace('https://', '').replace('http://', '')
        normalized = normalized.replace('www.', '')
        normalized = normalized.rstrip('/')
        
        return normalized

    def _normalize_name_for_matching(self, name: str) -> str:
        """Normalize name for strict matching (only exact matches)"""
        if not name:
            return ""
        
        # Only basic normalization - remove extra spaces and convert to lowercase
        normalized = name.lower().strip()
        normalized = ' '.join(normalized.split())  # Remove extra whitespace
        
        return normalized

    def _urls_are_similar(self, url1: str, url2: str) -> bool:
        """Check if two URLs are similar enough to be considered the same entity"""
        if not url1 or not url2:
            return False
        
        norm1 = self._normalize_url(url1)
        norm2 = self._normalize_url(url2)
        
        # Exact match
        if norm1 == norm2:
            return True
        
        # Check if one is a subdomain of the other
        if norm1 in norm2 or norm2 in norm1:
            # Extract domain parts
            domain1 = norm1.split('/')[0]
            domain2 = norm2.split('/')[0]
            
            # Check if they share the same root domain
            parts1 = domain1.split('.')
            parts2 = domain2.split('.')
            
            if len(parts1) >= 2 and len(parts2) >= 2:
                root1 = '.'.join(parts1[-2:])
                root2 = '.'.join(parts2[-2:])
                return root1 == root2
        
        return False

    def _fallback_entity_search(self, name: str, website_url: str) -> Optional[Dict[str, Any]]:
        """Fallback search method if caching fails"""
        try:
            self.logger.info("   🔄 Using fallback entity search...")
            
            headers = self.client._get_headers()
            
            # Quick search through first few pages only
            for page in range(1, 4):  # Only check first 3 pages
                try:
                    response = requests.get(
                        f"{self.client.base_url}/entities",
                        headers=headers,
                        params={"page": page, "limit": 50},
                        timeout=10
                    )
                    
                    if response.status_code == 200:
                        result = response.json()
                        entities = result.get('data', []) if isinstance(result, dict) else result
                        
                        if not entities:
                            break
                        
                        for entity in entities:
                            entity_url = entity.get('website_url', '').strip()
                            if website_url and entity_url and self._normalize_url(entity_url) == self._normalize_url(website_url):
                                self.logger.info(f"   ✅ Found existing entity via fallback: {entity.get('id')}")
                                return entity
                    
                except requests.exceptions.RequestException:
                    break
            
            return None
            
        except Exception as e:
            self.logger.error(f"   ❌ Error in fallback search: {str(e)}")
            return None

    def _improve_tag_classification(self, tool_name: str, initial_tags: List[str],
                                   categories: List[str], description: str,
                                   use_cases: List[str], key_features: List[str]) -> List[str]:
        """Improve tag classification to avoid generic tags like 'Cloud-Based'"""
        try:
            # COST OPTIMIZATION: Filter out generic tags first
            filtered_tags = [
                tag for tag in initial_tags
                if tag.lower() not in ['cloud-based', 'ai-powered', 'web-based', 'online', 'digital']
            ]

            # COST OPTIMIZATION: Skip AI if we have enough good tags
            if len(filtered_tags) >= 4:
                self.logger.info(f"   💰 COST SAVED: Skipping AI tag enhancement - already have {len(filtered_tags)} good tags")
                return filtered_tags[:6]  # Limit to 6 tags

            # If we have good initial tags, enhance them rather than replace
            if initial_tags and len(initial_tags) > 2:
                # If we still have good tags, use them as base
                if len(filtered_tags) >= 2:
                    base_tags = filtered_tags
                else:
                    base_tags = []
            else:
                base_tags = []

            # Prepare context for AI analysis
            context_parts = []
            if categories:
                context_parts.append(f"Categories: {', '.join(categories)}")
            if use_cases:
                context_parts.append(f"Use cases: {', '.join(use_cases[:3])}")  # Limit to 3
            if key_features:
                context_parts.append(f"Key features: {', '.join(key_features[:3])}")  # Limit to 3
            if description:
                context_parts.append(f"Description: {description[:200]}...")  # Limit length

            context = " | ".join(context_parts)
            existing_str = ", ".join(base_tags) if base_tags else "none"

            # AI prompt for improved tag classification
            prompt = f"""
            Analyze this AI tool and suggest 4-6 SPECIFIC, searchable tags that users would actually look for.

            Tool: {tool_name}
            Context: {context}
            Current tags: {existing_str}

            AVOID generic tags like: Cloud-Based, AI-Powered, Web-Based, Online, Digital, SaaS

            Focus on SPECIFIC tags that describe:
            - Technical approach (e.g., "Machine Learning", "Natural Language Processing", "Computer Vision")
            - Industry/domain (e.g., "Healthcare", "Finance", "E-commerce", "Education")
            - Use case type (e.g., "Content Creation", "Data Analysis", "Automation", "Customer Support")
            - User type (e.g., "Developers", "Marketers", "Designers", "Researchers")
            - Specific capabilities (e.g., "Real-time", "Multi-language", "API Integration", "No-Code")

            Return ONLY a comma-separated list of 4-6 specific tags, no explanations:
            """

            # Use the enrichment service to get AI response
            response = self.enrichment_service._call_perplexity(prompt)

            if response and isinstance(response, str):
                # Parse the response to extract tags
                tags_text = response.strip()

                # Clean up the response
                if '\n' in tags_text:
                    tags_text = tags_text.split('\n')[0]

                # Split by comma and clean up
                ai_tags = [
                    tag.strip().strip('"').strip("'")
                    for tag in tags_text.split(',')
                    if tag.strip() and len(tag.strip()) > 2
                ]

                # Combine base tags with AI suggestions, removing duplicates
                all_tags = base_tags + ai_tags
                unique_tags = []
                seen_lower = set()

                for tag in all_tags:
                    tag_lower = tag.lower()
                    if tag_lower not in seen_lower and tag_lower not in ['cloud-based', 'ai-powered', 'web-based', 'online', 'digital']:
                        unique_tags.append(tag)
                        seen_lower.add(tag_lower)

                final_tags = unique_tags[:6]  # Limit to 6 tags
                self.logger.info(f"   🏷️  Improved tags: {final_tags}")
                return final_tags

            # Fallback to filtered initial tags if AI fails
            return base_tags if base_tags else initial_tags

        except Exception as e:
            self.logger.error(f"   ❌ Error improving tag classification: {str(e)}")
            # Return filtered initial tags as fallback
            return [tag for tag in initial_tags if tag.lower() not in ['cloud-based', 'ai-powered', 'web-based']]

    def _extract_features_with_ai(self, tool_name: str, use_cases: List[str],
                                 description: str, existing_features: List[str]) -> List[str]:
        """Extract additional features using AI analysis of use cases and description"""
        try:
            # COST OPTIMIZATION: Skip AI if we already have enough features
            if len(existing_features) >= 4:
                self.logger.info(f"   💰 COST SAVED: Skipping AI feature extraction - already have {len(existing_features)} features")
                return []

            # Prepare context for AI analysis (limit to reduce tokens)
            context_parts = []
            if use_cases:
                context_parts.append(f"Use cases: {', '.join(use_cases[:2])}")  # Limit to 2 use cases
            if description:
                context_parts.append(f"Description: {description[:200]}")  # Limit description length

            if not context_parts:
                return []

            context = " | ".join(context_parts)
            existing_str = ", ".join(existing_features) if existing_features else "none"

            # AI prompt for feature extraction (COST OPTIMIZED - shorter prompt)
            prompt = f"""
            Tool: {tool_name}
            Context: {context}
            Existing: {existing_str}

            Extract 2-3 specific features (not already listed):
            """

            # Use the enrichment service to get AI response with reduced tokens
            response = self.enrichment_service._call_perplexity(prompt, max_tokens=100)

            if response and isinstance(response, str):
                # Parse the response to extract features
                features_text = response.strip()

                # Clean up the response - remove any extra text
                if '\n' in features_text:
                    features_text = features_text.split('\n')[0]

                # Split by comma and clean up
                new_features = [
                    feature.strip().strip('"').strip("'")
                    for feature in features_text.split(',')
                    if feature.strip() and len(feature.strip()) > 2
                ]

                # Filter out features that are too similar to existing ones
                filtered_features = []
                for feature in new_features:
                    if not any(existing.lower() in feature.lower() or feature.lower() in existing.lower()
                             for existing in existing_features):
                        filtered_features.append(feature)

                self.logger.info(f"   🤖 AI extracted {len(filtered_features)} new features: {filtered_features}")
                return filtered_features[:5]  # Limit to 5 features

            return []

        except Exception as e:
            self.logger.error(f"   ❌ Error extracting features with AI: {str(e)}")
            return []

    def _ensure_unique_name(self, name: str) -> str:
        """Ensure the entity name is unique by checking against existing entities"""
        try:
            # Check if the base name already exists
            if not self._name_exists_in_db(name):
                return name

            # If it exists, try variations
            for i in range(2, 11):  # Try up to 10 variations
                candidate_name = f"{name} {i}"
                if not self._name_exists_in_db(candidate_name):
                    return candidate_name

            # If all variations exist, use timestamp
            from datetime import datetime
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            return f"{name} {timestamp}"

        except Exception as e:
            self.logger.error(f"   ❌ Error ensuring unique name: {str(e)}")
            # Fallback to timestamp
            from datetime import datetime
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            return f"{name} {timestamp}"

    def _name_exists_in_db(self, name: str) -> bool:
        """Check if a name exists in the database"""
        try:
            headers = self.client._get_headers()

            # Check first few pages for name matches (API doesn't support search)
            page = 1
            page_size = 20
            max_pages = 3  # Check first 60 entities only for performance

            while page <= max_pages:
                response = requests.get(
                    f"{self.client.base_url}/entities",
                    headers=headers,
                    params={"page": page, "limit": page_size},
                    timeout=10
                )

                if response.status_code == 200:
                    result = response.json()
                    entities = result.get('data', []) if isinstance(result, dict) else result

                    if not entities:
                        break

                    for entity in entities:
                        if entity.get('name', '').lower().strip() == name.lower().strip():
                            return True

                    page += 1
                else:
                    break

            return False

        except Exception as e:
            self.logger.warning(f"   ⚠️  Error checking name existence: {str(e)}")
            return False  # Assume it doesn't exist if we can't check

    def _name_exists(self, tool_name: str) -> bool:
        """Check if an entity with this name already exists"""
        # This is a simplified check - in production you might want to query the API
        # For now, we'll assume we need unique names for the demo
        return False  # Always return False for now to let timestamp handle uniqueness
    
    def _normalize_pricing_model(self, pricing_model: str) -> str:
        """Normalize pricing model to match API enum values"""
        if not pricing_model:
            return 'FREEMIUM'  # Safe default

        pricing_model_clean = str(pricing_model).upper().strip()

        # Handle multiple values (take the first one)
        if '/' in pricing_model_clean:
            pricing_model_clean = pricing_model_clean.split('/')[0].strip()
        
        # Comprehensive mapping for pricing models
        pricing_map = {
            'FREE': 'FREE',
            'FREEMIUM': 'FREEMIUM', 
            'SUBSCRIPTION': 'SUBSCRIPTION',
            'PAID': 'SUBSCRIPTION',  # Map PAID to SUBSCRIPTION
            'MONTHLY': 'SUBSCRIPTION',
            'YEARLY': 'SUBSCRIPTION',
            'RECURRING': 'SUBSCRIPTION',
            'SaaS': 'SUBSCRIPTION',
            'PAY_PER_USE': 'PAY_PER_USE',
            'PAY PER USE': 'PAY_PER_USE',
            'PAYPERUSE': 'PAY_PER_USE',
            'USAGE_BASED': 'PAY_PER_USE',
            'CREDITS': 'PAY_PER_USE',
            'TOKENS': 'PAY_PER_USE',
            'ONE_TIME_PURCHASE': 'ONE_TIME_PURCHASE',
            'ONE-TIME': 'ONE_TIME_PURCHASE',
            'ONETIME': 'ONE_TIME_PURCHASE',
            'ONE TIME': 'ONE_TIME_PURCHASE',
            'PURCHASE': 'ONE_TIME_PURCHASE',
            'LIFETIME': 'ONE_TIME_PURCHASE',
            'CONTACT_SALES': 'CONTACT_SALES',
            'CONTACT': 'CONTACT_SALES',
            'ENTERPRISE': 'CONTACT_SALES',
            'CUSTOM': 'CONTACT_SALES',
            'QUOTE': 'CONTACT_SALES',
            'OPEN_SOURCE': 'OPEN_SOURCE',
            'OPEN': 'OPEN_SOURCE',
            'OSS': 'OPEN_SOURCE',
            'MIT': 'OPEN_SOURCE',
            'GPL': 'OPEN_SOURCE',
            'APACHE': 'OPEN_SOURCE'
        }
        
        # Check direct mapping first
        if pricing_model_clean in pricing_map:
            return pricing_map[pricing_model_clean]
        
        # Try partial matches
        if any(word in pricing_model_clean for word in ['FREE', 'NO COST', 'ZERO']):
            return 'FREE'
        elif any(word in pricing_model_clean for word in ['FREEMIUM', 'FREE TIER', 'FREE PLAN']):
            return 'FREEMIUM'
        elif any(word in pricing_model_clean for word in ['SUBSCRIPTION', 'MONTHLY', 'YEARLY', 'RECURRING']):
            return 'SUBSCRIPTION'
        elif any(word in pricing_model_clean for word in ['PAY PER', 'USAGE', 'CREDITS', 'TOKENS']):
            return 'PAY_PER_USE'
        elif any(word in pricing_model_clean for word in ['ONE TIME', 'LIFETIME', 'PURCHASE']):
            return 'ONE_TIME_PURCHASE'
        elif any(word in pricing_model_clean for word in ['CONTACT', 'ENTERPRISE', 'CUSTOM']):
            return 'CONTACT_SALES'
        elif any(word in pricing_model_clean for word in ['OPEN', 'SOURCE', 'MIT', 'GPL']):
            return 'OPEN_SOURCE'
        
        # Default to FREEMIUM as safe choice
        return 'FREEMIUM'
    
    def _normalize_price_range(self, price_range: str) -> str:
        """Normalize price range to match API enum values"""
        if not price_range or str(price_range).upper().strip() in ['UNKNOWN', 'N/A', 'NA', '']:
            return 'MEDIUM'

        price_range = str(price_range).upper().strip()

        # Handle multiple values (take the first one)
        if '|' in price_range:
            price_range = price_range.split('|')[0].strip()
        elif '/' in price_range:
            price_range = price_range.split('/')[0].strip()

        # Map to valid enum values with additional mappings
        price_map = {
            'FREE': 'FREE',
            'LOW': 'LOW',
            'MEDIUM': 'MEDIUM',
            'HIGH': 'HIGH',
            'ENTERPRISE': 'ENTERPRISE',
            'UNKNOWN': 'MEDIUM',  # Map UNKNOWN to MEDIUM
            'N/A': 'MEDIUM',
            'NA': 'MEDIUM',
            'NOT_SPECIFIED': 'MEDIUM',
            'VARIES': 'MEDIUM',
            'CONTACT': 'ENTERPRISE',
            'CUSTOM': 'ENTERPRISE'
        }

        return price_map.get(price_range, 'MEDIUM')  # Default to MEDIUM

    def _normalize_learning_curve(self, learning_curve: str) -> str:
        """Normalize learning curve to match API enum values"""
        if not learning_curve or str(learning_curve).upper().strip() in ['UNKNOWN', 'N/A', 'NA', '']:
            return 'MEDIUM'

        learning_curve = str(learning_curve).upper().strip()

        # Map to valid enum values
        curve_map = {
            'LOW': 'LOW',
            'MEDIUM': 'MEDIUM',
            'HIGH': 'HIGH',
            'EASY': 'LOW',  # Map old values to new
            'HARD': 'HIGH',  # Map old values to new
            'UNKNOWN': 'MEDIUM',
            'N/A': 'MEDIUM',
            'NA': 'MEDIUM',
            'SIMPLE': 'LOW',
            'BASIC': 'LOW',
            'BEGINNER': 'LOW',
            'INTERMEDIATE': 'MEDIUM',
            'MODERATE': 'MEDIUM',
            'STANDARD': 'MEDIUM',
            'ADVANCED': 'HIGH',
            'COMPLEX': 'HIGH',
            'DIFFICULT': 'HIGH',
            'EXPERT': 'HIGH'
        }

        return curve_map.get(learning_curve, 'MEDIUM')  # Default to MEDIUM

    def _normalize_employee_count(self, employee_count: str) -> Optional[str]:
        """Normalize employee count to match API enum values"""
        if not employee_count or employee_count.lower() in ['unknown', 'n/a', 'na', '']:
            return None
            
        employee_count_clean = str(employee_count).upper().strip()
        
        # Direct mapping for exact matches
        count_map = {
            '1-10': 'C1_10',
            '11-50': 'C11_50', 
            '51-200': 'C51_200',
            '201-500': 'C201_500',
            '501-1000': 'C501_1000',
            '1001-5000': 'C1001_5000',
            '5000+': 'C5001_PLUS',
            '5001+': 'C5001_PLUS',
            '500+': 'C501_1000',
            '1000+': 'C1001_5000',
            'UNKNOWN': None,
            'SMALL': 'C1_10',
            'MEDIUM': 'C11_50',
            'LARGE': 'C201_500',
            'ENTERPRISE': 'C5001_PLUS'
        }
        
        # Check direct mapping first
        if employee_count_clean in count_map:
            return count_map[employee_count_clean]
        
        # Try to extract number and map to range
        import re
        numbers = re.findall(r'\d+', employee_count_clean)
        if numbers:
            count = int(numbers[0])
            if count <= 10:
                return 'C1_10'
            elif count <= 50:
                return 'C11_50'
            elif count <= 200:
                return 'C51_200'
            elif count <= 500:
                return 'C201_500'
            elif count <= 1000:
                return 'C501_1000'
            elif count <= 5000:
                return 'C1001_5000'
            else:
                return 'C5001_PLUS'
        
        # Default to None if can't parse
        return None
    
    def _normalize_funding_stage(self, funding_stage: str) -> Optional[str]:
        """Normalize funding stage to match API enum values"""
        if not funding_stage or funding_stage.lower() in ['unknown', 'n/a', 'na', '']:
            return None
            
        funding_stage_clean = str(funding_stage).upper().strip().replace('-', '_').replace(' ', '_')
        
        # Comprehensive mapping for funding stages
        stage_map = {
            'PRE_SEED': 'PRE_SEED',
            'PRESEED': 'PRE_SEED',
            'PRE SEED': 'PRE_SEED',
            'SEED': 'SEED',
            'SERIES_A': 'SERIES_A',
            'SERIES A': 'SERIES_A',
            'SERIESA': 'SERIES_A',
            'A': 'SERIES_A',
            'SERIES_B': 'SERIES_B', 
            'SERIES B': 'SERIES_B',
            'SERIESB': 'SERIES_B',
            'B': 'SERIES_B',
            'SERIES_C': 'SERIES_C',
            'SERIES C': 'SERIES_C',
            'SERIESC': 'SERIES_C',
            'C': 'SERIES_C',
            'SERIES_D': 'SERIES_D_PLUS',
            'SERIES D': 'SERIES_D_PLUS',
            'SERIESD': 'SERIES_D_PLUS',
            'D': 'SERIES_D_PLUS',
            'SERIES_D_PLUS': 'SERIES_D_PLUS',
            'SERIES_E': 'SERIES_D_PLUS',
            'SERIES E': 'SERIES_D_PLUS',
            'LATE_STAGE': 'SERIES_D_PLUS',
            'GROWTH': 'SERIES_D_PLUS',
            'PUBLIC': 'PUBLIC',
            'IPO': 'PUBLIC',
            'PUBLICLY_TRADED': 'PUBLIC',
            'ACQUIRED': 'PUBLIC',
            'BOOTSTRAPPED': None,
            'SELF_FUNDED': None,
            'STEALTH': 'PRE_SEED',
            'ANGEL': 'PRE_SEED',
            'FRIENDS_AND_FAMILY': 'PRE_SEED',
            'UNKNOWN': None,
            'NONE': None,
            'NOT_DISCLOSED': None,
            'PRIVATE': None
        }
        
        # Check direct mapping first
        if funding_stage_clean in stage_map:
            return stage_map[funding_stage_clean]
        
        # Try partial matches for complex stages
        if 'PRE' in funding_stage_clean and 'SEED' in funding_stage_clean:
            return 'PRE_SEED'
        elif 'SEED' in funding_stage_clean:
            return 'SEED'
        elif 'SERIES' in funding_stage_clean:
            if 'A' in funding_stage_clean:
                return 'SERIES_A'
            elif 'B' in funding_stage_clean:
                return 'SERIES_B'
            elif 'C' in funding_stage_clean:
                return 'SERIES_C'
            elif any(letter in funding_stage_clean for letter in ['D', 'E', 'F']):
                return 'SERIES_D_PLUS'
        elif any(word in funding_stage_clean for word in ['PUBLIC', 'IPO', 'TRADED']):
            return 'PUBLIC'
        
        # Default to None if can't parse
        return None
    
    def _get_entity_type_id(self) -> str:
        """Get the entity type ID for AI tools dynamically from API"""
        entity_type_id = self.client.get_ai_tool_entity_type_id()
        if entity_type_id:
            return entity_type_id
        else:
            self.logger.error("Could not get AI tool entity type ID from API")
            # Fallback to known ID if API call fails
            return "fd181400-c9e6-431c-a8bd-c068d0491aba"
    
    def _ensure_accurate_name(self, enriched_data: Dict[str, Any], tool_name: str) -> str:
        """Ensure we have the most accurate tool name possible"""
        # Priority: 1. Enhanced name, 2. Original tool name, 3. Cleaned version
        name = enriched_data.get('name', tool_name).strip()
        
        # Clean up common issues
        name = re.sub(r'\s+', ' ', name)  # Multiple spaces
        name = re.sub(r'[^\w\s\-\.\(\)&]', '', name)  # Invalid characters
        
        # Ensure reasonable length
        if len(name) > 100:
            name = name[:97] + "..."
        
        return name if name else tool_name

    def _create_accurate_short_description(self, enriched_data: Dict[str, Any], tool_name: str) -> str:
        """Create the most accurate and compelling short description"""
        short_desc = enriched_data.get('short_description', '')
        
        if not short_desc:
            # Generate from longer description
            full_desc = enriched_data.get('description', '')
            if full_desc:
                # Take first sentence or first 150 chars
                sentences = full_desc.split('. ')
                short_desc = sentences[0]
                if len(short_desc) > 150:
                    short_desc = short_desc[:147] + "..."
            else:
                # Fallback generic description
                short_desc = f"{tool_name} - AI-powered tool for enhanced productivity"
        
        # Ensure proper length and quality
        if len(short_desc) > 200:
            short_desc = short_desc[:197] + "..."
        
        return short_desc.strip()

    def _create_comprehensive_description(self, enriched_data: Dict[str, Any], website_data: Dict[str, Any]) -> str:
        """Create the most comprehensive and accurate description possible"""
        # Combine multiple sources for maximum detail
        descriptions = []
        
        # Primary enhanced description
        if enriched_data.get('description'):
            descriptions.append(enriched_data['description'])
        
        # Website extracted description
        if website_data.get('description') and website_data['description'] not in descriptions:
            descriptions.append(website_data['description'])
        
        # Key features as description enhancement
        if enriched_data.get('key_features'):
            features_text = " ".join([f"Features include {', '.join(enriched_data['key_features'][:5])}"])
            descriptions.append(features_text)
        
        # Use cases as description enhancement  
        if enriched_data.get('use_cases'):
            use_cases_text = f"Use cases include {', '.join(enriched_data['use_cases'][:3])}"
            descriptions.append(use_cases_text)
        
        # Combine and clean
        full_description = ". ".join(descriptions).strip()
        
        # Ensure reasonable length
        if len(full_description) > 1000:
            full_description = full_description[:997] + "..."
        
        return full_description

    def _determine_accurate_free_tier(self, enriched_data: Dict[str, Any]) -> bool:
        """Determine if the tool has a free tier with maximum accuracy"""
        # Check multiple indicators
        has_free = enriched_data.get('has_free_tier', False)
        pricing_model = enriched_data.get('pricing_model', '').lower()
        pricing_details = enriched_data.get('pricing_details', '').lower()
        
        # Look for free indicators
        free_indicators = ['free', 'freemium', 'trial', 'no cost', 'open source']
        if any(indicator in pricing_model for indicator in free_indicators):
            return True
        if any(indicator in pricing_details for indicator in free_indicators):
            return True
            
        return has_free

    def _calculate_accurate_rating(self, enriched_data: Dict[str, Any]) -> Optional[float]:
        """Calculate the most accurate rating possible"""
        # Try multiple rating sources
        rating = enriched_data.get('review_sentiment_score')
        
        if rating:
            # Ensure rating is in 1-5 range
            if isinstance(rating, (int, float)):
                if rating > 5:
                    rating = rating / 2  # Convert from 10-point scale
                if rating > 5:
                    rating = 5.0  # Cap at 5
                if rating < 1:
                    rating = None  # Invalid rating
                return round(float(rating), 1)
        
        # Look for sentiment label as backup
        sentiment = enriched_data.get('review_sentiment_label', '').lower()
        if sentiment == 'positive':
            return 4.0
        elif sentiment == 'very positive':
            return 4.5
        elif sentiment == 'neutral':
            return 3.0
        elif sentiment == 'negative':
            return 2.0
            
        return None

    def _extract_accurate_review_count(self, enriched_data: Dict[str, Any]) -> Optional[int]:
        """Extract the most accurate review count possible"""
        review_count = enriched_data.get('review_count')
        
        if review_count:
            # Handle "Unknown" string
            if isinstance(review_count, str) and review_count.lower() in ['unknown', 'none', '']:
                return None
                
            try:
                count = int(review_count)
                return count if count >= 0 else None
            except (ValueError, TypeError):
                # Try to extract number from string
                if isinstance(review_count, str):
                    import re
                    numbers = re.findall(r'\d+', review_count)
                    if numbers:
                        return int(numbers[0])
        
        return None

    def _create_compelling_short_description(self, enriched_data: Dict[str, Any], tool_name: str) -> str:
        """Create compelling, specific short descriptions - NO MORE LAZY 'AI Tool' descriptions!"""
        short_desc = enriched_data.get('short_description', '')
        
        if not short_desc or f"{tool_name} - AI-powered tool" in short_desc or f"{tool_name} - AI Tool" in short_desc:
            # Generate much better short description from available data
            categories = enriched_data.get('categories', [])
            key_features = enriched_data.get('key_features', [])
            use_cases = enriched_data.get('use_cases', [])
            
            if key_features and len(key_features) > 0:
                primary_feature = key_features[0]
                if categories:
                    short_desc = f"{tool_name} - {primary_feature} for {categories[0].lower()}"
                else:
                    short_desc = f"{tool_name} - {primary_feature} tool"
            elif use_cases and len(use_cases) > 0:
                primary_use = use_cases[0]
                short_desc = f"{tool_name} - AI solution for {primary_use.lower()}"
            elif categories:
                short_desc = f"{tool_name} - Advanced {categories[0].lower()} platform"
            else:
                # Last resort - but still better than generic
                short_desc = f"{tool_name} - Professional AI-powered automation platform"
        
        # Ensure quality length and format
        if len(short_desc) > 200:
            short_desc = short_desc[:197] + "..."
        
        return short_desc.strip()

    def _create_seo_meta_description(self, enriched_data: Dict[str, Any], tool_name: str) -> str:
        """Create compelling SEO meta description"""
        key_features = enriched_data.get('key_features', [])
        categories = enriched_data.get('categories', [])
        
        if key_features and categories:
            return f"Discover {tool_name}, the leading {categories[0].lower()} solution featuring {', '.join(key_features[:2])}. Transform your workflow with AI-powered automation."
        elif key_features:
            return f"Explore {tool_name} - advanced AI tool with {', '.join(key_features[:3])}. Boost productivity and streamline operations."
        else:
            return f"Learn about {tool_name}, a comprehensive AI solution designed to enhance efficiency and drive innovation in your business."

    def _extract_social_links(self, enriched_data: Dict[str, Any], website_data: Dict[str, Any]) -> Dict[str, str]:
        """Extract social media links from multiple sources"""
        social_links = {}
        
        # From enriched data
        if enriched_data.get('social_links'):
            social_links.update(enriched_data['social_links'])
        
        # From website data
        if website_data.get('social_links'):
            social_links.update(website_data['social_links'])
        
        # Common social platforms to look for
        social_patterns = {
            'twitter': ['twitter.com/', 'x.com/'],
            'linkedin': ['linkedin.com/company/', 'linkedin.com/in/'],
            'github': ['github.com/'],
            'youtube': ['youtube.com/'],
            'facebook': ['facebook.com/'],
            'instagram': ['instagram.com/']
        }
        
        # Extract from any text fields
        all_text = f"{enriched_data} {website_data}".lower()
        for platform, patterns in social_patterns.items():
            if platform not in social_links:
                for pattern in patterns:
                    if pattern in all_text:
                        # Extract the handle/username (simplified)
                        start = all_text.find(pattern)
                        if start != -1:
                            end = all_text.find(' ', start)
                            if end == -1:
                                end = start + 100  # reasonable limit
                            social_url = all_text[start:end].strip()
                            if social_url:
                                social_links[platform] = social_url.split('/')[-1]  # Get handle
                                break
        
        return social_links

    def _build_comprehensive_tool_details(self, enriched_data: Dict[str, Any], website_data: Dict[str, Any]) -> Dict[str, Any]:
        """Build the nested tool_details object with comprehensive and validated data."""
        
        details = {
            # Enums & Normalized Fields
            "learning_curve": self._normalize_learning_curve(enriched_data.get('learning_curve')),
            "technical_level": self._normalize_technical_level(enriched_data.get('technical_level')),
            "pricing_model": self._normalize_pricing_model(enriched_data.get('pricing_model')),
            "price_range": self._normalize_price_range(enriched_data.get('price_range')),
            "customization_level": self._normalize_enum_string(enriched_data.get('customization_level')),

            # Booleans
            "has_free_tier": self._determine_accurate_free_tier(enriched_data),
            "mobile_support": enriched_data.get('mobile_support', False),
            "api_access": enriched_data.get('api_access', False),
            "has_api": enriched_data.get('api_access', False), # Duplicate for now
            "trial_available": enriched_data.get('trial_available', False),
            "demo_available": enriched_data.get('demo_available', False),
            "open_source": enriched_data.get('open_source', False),
            "has_live_chat": website_data.get('has_live_chat', False),

            # Text & URLs
            "pricing_details": enriched_data.get('pricing_details'),
            "pricing_url": self._validate_url(enriched_data.get('pricing_url')),
            "support_email": website_data.get('support_email'),
            "community_url": self._validate_url(website_data.get('community_url')),
            "api_documentation_url": self._validate_url(enriched_data.get('api_documentation_url')),

            # Arrays
            "key_features": enriched_data.get('key_features', []),
            "use_cases": enriched_data.get('use_cases', []),
            "integrations": enriched_data.get('integrations', []),
            "target_audience": enriched_data.get('target_audience', []),
            "platforms": enriched_data.get('platforms', []),
            "support_channels": enriched_data.get('support_channels', []),
            "programming_languages": enriched_data.get('programming_languages', []),
            "frameworks": enriched_data.get('frameworks', []),
            "libraries": enriched_data.get('libraries', []),
            "deployment_options": enriched_data.get('deployment_options', []),
            "supported_os": enriched_data.get('supported_os', [])
        }
        
        return {k: v for k, v in details.items() if v is not None and v != '' and v != []}

    def _normalize_founded_year(self, founded_year) -> Optional[int]:
        """Normalize founded year to valid integer"""
        if not founded_year or founded_year == "Unknown":
            return None
        
        try:
            year = int(founded_year)
            # Validate reasonable range
            if 1900 <= year <= 2025:
                return year
            else:
                return None
        except (ValueError, TypeError):
            # Extract year from string
            if isinstance(founded_year, str):
                import re
                years = re.findall(r'\b(19|20)\d{2}\b', founded_year)
                if years:
                    year = int(years[0])
                    if 1900 <= year <= 2025:
                        return year
            return None

    def _normalize_sentiment_score(self, score) -> Optional[float]:
        """Normalize sentiment score to 0.0-1.0 range"""
        if not score:
            return None
        
        try:
            score_float = float(score)
            # Convert from different scales to 0.0-1.0
            if score_float > 1.0:
                if score_float <= 5.0:
                    # Convert from 1-5 scale to 0-1 scale
                    return round((score_float - 1) / 4, 2)
                elif score_float <= 10.0:
                    # Convert from 1-10 scale to 0-1 scale
                    return round((score_float - 1) / 9, 2)
                else:
                    # Cap at 1.0
                    return 1.0
            else:
                # Already in 0-1 range
                return round(score_float, 2)
        except (ValueError, TypeError):
            return None

    def _validate_url(self, url: str) -> Optional[str]:
        """Validate URL format"""
        if not url or url == "Unknown" or url == "":
            return None
        
        if not isinstance(url, str):
            return None
        
        # Basic URL validation
        if url.startswith(('http://', 'https://')):
            return url
        elif url.startswith('//'):
            return f'https:{url}'
        elif '.' in url and not url.startswith('www.'):
            return f'https://{url}'
        else:
            return None

    def _create_compelling_short_description(self, enriched_data: Dict[str, Any], tool_name: str) -> str:
        """Create compelling, specific short descriptions - NO MORE LAZY 'AI Tool' descriptions!"""
        short_desc = enriched_data.get('short_description', '')
        
        if not short_desc or f"{tool_name} - AI-powered tool" in short_desc or f"{tool_name} - AI Tool" in short_desc:
            # Generate much better short description from available data
            categories = enriched_data.get('categories', [])
            key_features = enriched_data.get('key_features', [])
            use_cases = enriched_data.get('use_cases', [])
            
            if key_features and len(key_features) > 0:
                primary_feature = key_features[0]
                if categories:
                    short_desc = f"{tool_name} - {primary_feature} for {categories[0].lower()}"
                else:
                    short_desc = f"{tool_name} - {primary_feature} tool"
            elif use_cases and len(use_cases) > 0:
                primary_use = use_cases[0]
                short_desc = f"{tool_name} - AI solution for {primary_use.lower()}"
            elif categories:
                short_desc = f"{tool_name} - Advanced {categories[0].lower()} platform"
            else:
                # Last resort - but still better than generic
                short_desc = f"{tool_name} - Professional AI-powered automation platform"
        
        # Ensure quality length and format
        if len(short_desc) > 200:
            short_desc = short_desc[:197] + "..."
        
        return short_desc.strip()

    def _create_seo_meta_description(self, enriched_data: Dict[str, Any], tool_name: str) -> str:
        """Create compelling SEO meta description"""
        key_features = enriched_data.get('key_features', [])
        categories = enriched_data.get('categories', [])
        
        if key_features and categories:
            return f"Discover {tool_name}, the leading {categories[0].lower()} solution featuring {', '.join(key_features[:2])}. Transform your workflow with AI-powered automation."
        elif key_features:
            return f"Explore {tool_name} - advanced AI tool with {', '.join(key_features[:3])}. Boost productivity and streamline operations."
        else:
            return f"Learn about {tool_name}, a comprehensive AI solution designed to enhance efficiency and drive innovation in your business."

    def _extract_social_links(self, enriched_data: Dict[str, Any], website_data: Dict[str, Any]) -> Dict[str, str]:
        """Extract social media links from multiple sources"""
        social_links = {}
        
        # From enriched data
        if enriched_data.get('social_links'):
            for platform, handle in enriched_data['social_links'].items():
                if handle and handle != "Unknown" and len(handle) > 0:
                    # Clean up the handle
                    clean_handle = str(handle).strip()
                    if clean_handle and not clean_handle.lower() in ['unknown', 'none', '']:
                        social_links[platform] = clean_handle
        
        # From website data
        if website_data.get('social_links'):
            for platform, handle in website_data['social_links'].items():
                if platform not in social_links and handle and handle != "Unknown":
                    clean_handle = str(handle).strip()
                    if clean_handle and not clean_handle.lower() in ['unknown', 'none', '']:
                        social_links[platform] = clean_handle
        
        return social_links



    def _normalize_technical_level(self, technical_level: str) -> str:
        """Normalize technical level to enum values"""
        if not technical_level:
            return "BEGINNER"
        level = str(technical_level).strip().upper()
        if level in ["BEGINNER", "EASY", "NOVICE"]:
            return "BEGINNER"
        if level in ["INTERMEDIATE", "MEDIUM"]:
            return "INTERMEDIATE"
        if level in ["ADVANCED", "EXPERT", "HARD"]:
            return "ADVANCED"
        return "BEGINNER"  # Fallback

    def _normalize_enum_string(self, value: Optional[str]) -> Optional[str]:
        """Generic function to normalize a string to an ENUM-safe format."""
        if not value or not isinstance(value, str):
            return None
        # Converts "Some Value" to "SOME_VALUE"
        return value.strip().upper().replace(" ", "_").replace("-", "_")

    def _clean_null_values(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Recursively remove keys with None values from a dictionary."""
        if not isinstance(data, dict):
            return data
        return {key: self._clean_null_values(value) for key, value in data.items() if value is not None}
